import React from 'react';
import { DownloadStatus } from '../types';
import type { DownloadItem, DownloadProgress } from '../types';
import DownloadButton from './DownloadButton';
import DownloadContentCard from './DownloadContentCard';

interface DownloadCardProps {
  downloadData: DownloadItem | null;
  downloadProgress: DownloadProgress;
  startDownloadWithData: (data: DownloadItem) => Promise<void>;
  saveToLocal: () => Promise<void>;
  pauseDownload: () => void;
  resumeDownload: () => void;
  savedFiles: Set<string>;
  updateDownloadDataFilename: (filename: string) => void;
}

const DownloadCard: React.FC<DownloadCardProps> = ({
  downloadData,
  downloadProgress,
  startDownloadWithData,
  saveToLocal,
  pauseDownload,
  resumeDownload,
  savedFiles,
  updateDownloadDataFilename
}) => {

  // 获取当前下载状态 - 无状态时返回 null
  const getCurrentDownloadStatus = (): DownloadStatus | null => {
    if (!downloadData) return null;

    const progress = downloadProgress[downloadData.requestId];
    if (!progress) {
      // 检查是否已保存
      if (savedFiles.has(downloadData.requestId)) {
        return DownloadStatus.SAVED;
      }
      return null;
    }

    return progress.status;
  };

  // 检查是否有下载中的任务
  // const isDownloading = Object.values(downloadProgress).some(
  //   progress => progress.status === DownloadStatus.DOWNLOADING
  // );

  // 按钮点击处理函数
  const handlePause = () => {
    console.log('暂停下载');
    pauseDownload();
  };

  const handleRetry = async () => {
    console.log('重试下载');
    if (downloadData) {
      await startDownloadWithData(downloadData);
    }
  };

  const handleContinue = () => {
    console.log('继续下载');
    resumeDownload();
  };

  const handleSave = async () => {
    console.log('保存到本地');
    if (downloadData) {
      await saveToLocal();
    }
  };

  const handleSaved = async () => {
    console.log('重新保存文件');
    // 已保存状态下点击按钮，触发重新保存
    if (downloadData) {
      await saveToLocal();
    }
  };

  const handleTitleChange = (newTitle: string) => {
    console.log('标题已更改为:', newTitle);
    // 更新下载项的文件名
    updateDownloadDataFilename(newTitle);
  };

  // 渲染下载按钮
  const renderDownloadButton = () => {
    const currentStatus = getCurrentDownloadStatus();

    // 无状态时不显示按钮
    if (!currentStatus) {
      return null;
    }

    return (
      <DownloadButton
        status={currentStatus}
        onPause={handlePause}
        onRetry={handleRetry}
        onContinue={handleContinue}
        onSave={currentStatus === DownloadStatus.SAVED ? handleSaved : handleSave}
      />
    );
  };

  return (
    <div className="flex flex-col justify-between items-center p-6 gap-4 relative w-[1024px] h-80 bg-white shadow-lg rounded-lg">
      {/* 下载内容区域 */}

      {downloadData && (
        <DownloadContentCard
          downloadItem={downloadData}
          progress={downloadProgress[downloadData.requestId] || {
            percentage: 0,
            downloadedSize: 0,
            totalSize: 0,
            speed: 0,
            status: getCurrentDownloadStatus() || DownloadStatus.COMPLETED, // 无状态时显示完成状态，用于显示内容
            statusText: '等待下载'
          }}
          onTitleChange={handleTitleChange}
        />
      )}

      {/* 根据当前状态显示对应的下载按钮 */}
      <div className="flex flex-col gap-3">
        {renderDownloadButton()}
      </div>
    </div>
  );
};

export default DownloadCard;
