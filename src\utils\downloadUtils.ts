import type { DownloadItem, DownloadConfig, DownloadResponse, IDownloadController, DownloadControllerStatus, DownloadResult } from '../types';
import { DownloadControllerStatus as Status } from '../types';
import { sendToContentScript } from './messageUtils';

// 下载控制器类
export class DownloadController implements IDownloadController {
  public status: DownloadControllerStatus = Status.IDLE;
  public progress = {
    percentage: 0,
    downloadedSize: 0,
    totalSize: undefined as number | undefined,
    speed: 0
  };

  private config: DownloadConfig;
  private abortController?: AbortController;
  private reader?: ReadableStreamDefaultReader<Uint8Array>;
  private response?: Response;
  private chunks: Uint8Array[] = [];
  private isPaused = false;
  private lastLogTime = 0;
  private lastReceivedLength = 0;
  private pauseAbortController?: AbortController;

  public onProgress?: (progress: { percentage: number; downloadedSize: number; totalSize?: number; speed?: number }) => void;
  public onStatusChange?: (status: DownloadControllerStatus) => void;

  constructor(config: DownloadConfig) {
    this.config = config;
  }

  private updateStatus(newStatus: DownloadControllerStatus) {
    this.status = newStatus;
    if (this.onStatusChange) {
      this.onStatusChange(newStatus);
    }
  }

  private updateProgress(update: Partial<typeof this.progress>) {
    Object.assign(this.progress, update);
    if (this.onProgress) {
      this.onProgress(this.progress);
    }
  }

  async start(): Promise<DownloadResult> {
    try {
      this.updateStatus(Status.DOWNLOADING);
      this.abortController = new AbortController();

      console.log(`开始下载: ${this.config.filename}`);

      this.response = await fetch(this.config.url, {
        method: 'GET',
        credentials: 'omit',
        cache: 'no-cache',
        signal: this.abortController.signal
      });

      if (!this.response.ok) {
        throw new Error(`HTTP ${this.response.status}: ${this.response.statusText}`);
      }

      // 获取文件信息
      const contentLength = this.response.headers.get('content-length');
      const totalSize = contentLength ? parseInt(contentLength) : undefined;
      const contentType = this.response.headers.get('content-type') || 'application/octet-stream';

      this.progress.totalSize = totalSize;
      console.log(`文件信息: 大小=${totalSize ? (totalSize / 1024 / 1024).toFixed(1) + 'MB' : '未知'}, 类型=${contentType}`);

      // 获取响应流
      this.reader = this.response.body?.getReader();
      if (!this.reader) {
        throw new Error('无法获取响应流');
      }

      // 读取数据
      await this.readStream();

      // 下载完成，创建结果
      return this.createDownloadResult(contentType);

    } catch (error) {
      this.updateStatus(Status.ERROR);
      console.error('下载失败:', error);
      return {
        success: false,
        filename: this.config.filename,
        error: (error as Error).message
      };
    }
  }

  private createDownloadResult(contentType: string): DownloadResult {
    // 下载完成，创建 blob URL（直接使用chunks避免额外内存占用）
    const blob = new Blob(this.chunks, { type: contentType });
    const blobUrl = URL.createObjectURL(blob);

    this.updateStatus(Status.COMPLETED);
    console.log(`下载完成: ${this.config.filename}, 大小: ${(this.progress.downloadedSize / 1024 / 1024).toFixed(1)}MB`);

    return {
      success: true,
      filename: this.config.filename,
      blobUrl
    };
  }

  private async readStream(): Promise<void> {
    if (!this.reader) return;

    this.lastLogTime = Date.now();
    this.lastReceivedLength = 0;

    try {
      while (true) {
        const { done, value } = await this.reader.read();

        if (done) break;

        if (value) {
          this.chunks.push(value);
          this.progress.downloadedSize += value.length;

          const currentTime = Date.now();
          const percentage = this.progress.totalSize ?
            (this.progress.downloadedSize / this.progress.totalSize) * 100 : 0;

          // 每500ms更新一次进度
          if (currentTime - this.lastLogTime >= 500) {
            const timeDiff = (currentTime - this.lastLogTime) / 1000;
            const sizeDiff = this.progress.downloadedSize - this.lastReceivedLength;
            const speed = sizeDiff / timeDiff;

            this.updateProgress({
              percentage,
              speed
            });

            console.log(`下载进度: ${percentage.toFixed(1)}% (${(this.progress.downloadedSize / 1024 / 1024).toFixed(1)}MB${this.progress.totalSize ? '/' + (this.progress.totalSize / 1024 / 1024).toFixed(1) + 'MB' : ''}) 速度: ${formatSpeed(speed)}`);

            this.lastLogTime = currentTime;
            this.lastReceivedLength = this.progress.downloadedSize;
          }
        }
      }

      // 最后一次进度更新
      this.updateProgress({
        percentage: 100,
        speed: 0
      });
    } catch (error) {
      // 检查是否是用户主动暂停导致的中断
      if (error instanceof Error && error.name === 'AbortError') {
        console.log('下载被中断（暂停）');
        return; // 暂停时不抛出错误
      }
      throw error; // 其他错误继续抛出
    }
  }

  pause(): void {
    if (this.status === Status.DOWNLOADING) {
      this.isPaused = true;
      this.updateStatus(Status.PAUSED);

      // 中断当前的网络请求
      if (this.abortController) {
        this.abortController.abort();
      }

      // 关闭reader
      if (this.reader) {
        this.reader.cancel();
        this.reader = undefined;
      }

      console.log('下载已暂停');
    }
  }

  async resume(): Promise<void> {
    if (this.status === Status.PAUSED && this.isPaused) {
      this.isPaused = false;
      this.updateStatus(Status.DOWNLOADING);

      try {
        // 创建新的AbortController
        this.abortController = new AbortController();

        // 使用Range请求从断点继续下载
        const rangeStart = this.progress.downloadedSize;
        const headers: Record<string, string> = {
          'Range': `bytes=${rangeStart}-`
        };

        console.log(`从断点继续下载: ${rangeStart} bytes`);

        this.response = await fetch(this.config.url, {
          method: 'GET',
          credentials: 'omit',
          cache: 'no-cache',
          headers,
          signal: this.abortController.signal
        });

        // 检查服务器是否支持Range请求
        if (this.response.status === 206) {
          // 支持断点续传
          console.log('服务器支持断点续传，继续下载');
        } else if (this.response.status === 200) {
          // 不支持断点续传，需要重新开始
          console.log('服务器不支持断点续传，重新开始下载');
          this.chunks = [];
          this.progress.downloadedSize = 0;
          this.lastReceivedLength = 0;
        } else {
          throw new Error(`HTTP ${this.response.status}: ${this.response.statusText}`);
        }

        // 获取新的reader
        this.reader = this.response.body?.getReader();
        if (!this.reader) {
          throw new Error('无法获取响应流');
        }

        // 继续读取数据
        await this.readStream();

        // 如果下载完成，获取内容类型并创建结果
        if (this.status === Status.DOWNLOADING) {
          const contentType = this.response.headers.get('content-type') || 'application/octet-stream';
          this.createDownloadResult(contentType);
        }

      } catch (error) {
        this.updateStatus(Status.ERROR);
        console.error('恢复下载失败:', error);
        throw error;
      }
    }
  }

  cancel(): void {
    if (this.abortController) {
      this.abortController.abort();
    }
    this.updateStatus(Status.ERROR);
    console.log('下载已取消');
  }
}

// 通过内容脚本设置请求头（为后续下载做准备）
export function downloadFileWithHeaders(data: DownloadItem, pageTaskId: string): Promise<DownloadResponse> {
  return new Promise((resolve, reject) => {
    const messageListener = (event: Event) => {
      const responseData = (event as CustomEvent).detail;

      window.removeEventListener('DOWNLOAD_FILE_RESPONSE', messageListener);

      if (responseData.success) {
        resolve(responseData);
      } else {
        reject(new Error(responseData.error || '下载文件失败'));
      }
    };

    window.addEventListener('DOWNLOAD_FILE_RESPONSE', messageListener);

    // 向内容脚本发送请求，包含页面任务ID
    sendToContentScript({
      type: 'DOWNLOAD_FILE_WITH_HEADERS',
      data: {
        requestId: data.requestId,
        url: data.url,
        filename: data.filename,
        requestHeaders: data.requestHeaders,
        pageUrl: data.pageUrl,
        pageTaskId: pageTaskId // 添加页面任务ID
      }
    });
  });
}

// 网页端下载处理函数（使用fetch + URL.createObjectURL）
export async function downloadInWebpage(
  config: DownloadConfig,
  onProgress?: (progress: { percentage: number; downloadedSize: number; totalSize?: number; speed?: number }) => void
): Promise<{ success: boolean; filename: string }> {
  try {
    console.log(`开始网页端下载: ${config.filename}`);

    // 在网页环境中发起fetch请求（会自动使用设置的请求头）
    const response = await fetch(config.url, {
      method: 'GET',
      credentials: 'omit', // 改为omit避免CORS问题
      cache: 'no-cache'
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    // 获取文件大小和类型
    const contentLength = response.headers.get('content-length');
    const totalSize = contentLength ? parseInt(contentLength) : undefined;
    const contentType = response.headers.get('content-type') || 'application/octet-stream';

    console.log(`文件信息: 大小=${totalSize ? (totalSize / 1024 / 1024).toFixed(1) + 'MB' : '未知'}, 类型=${contentType}`);

    // 获取响应流
    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('无法获取响应流');
    }

    let receivedLength = 0;
    const chunks: Uint8Array[] = [];
    let lastLogTime = Date.now();
    let lastReceivedLength = 0;

    // 读取流数据
    while (true) {
      const { done, value } = await reader.read();

      if (done) break;

      if (value) {
        chunks.push(value);
        receivedLength += value.length;

        const currentTime = Date.now();
        const percentage = totalSize ? (receivedLength / totalSize) * 100 : 0;

        // 每200ms打印一次日志和更新进度
        if (currentTime - lastLogTime >= 200) {
          const timeDiff = (currentTime - lastLogTime) / 1000; // 转换为秒
          const sizeDiff = receivedLength - lastReceivedLength;
          const speed = sizeDiff / timeDiff; // 字节/秒

          console.log(`下载进度: ${percentage.toFixed(1)}% (${(receivedLength / 1024 / 1024).toFixed(1)}MB${totalSize ? '/' + (totalSize / 1024 / 1024).toFixed(1) + 'MB' : ''}) 速度: ${formatSpeed(speed)}`);

          // 调用进度回调
          if (onProgress) {
            onProgress({
              percentage,
              downloadedSize: receivedLength,
              totalSize,
              speed
            });
          }

          lastLogTime = currentTime;
          lastReceivedLength = receivedLength;
        }
      }
    }

    // 确保最后一次进度更新
    if (onProgress) {
      onProgress({
        percentage: 100,
        downloadedSize: receivedLength,
        totalSize: receivedLength,
        speed: 0
      });
    }

    console.log(`下载完成，准备保存文件: ${config.filename}, 大小: ${(receivedLength / 1024 / 1024).toFixed(1)}MB`);

    // 创建Blob和Object URL（直接使用chunks避免额外内存占用）
    const blob = new Blob(chunks, { type: contentType });
    const objectUrl = URL.createObjectURL(blob);

    console.log(`准备保存文件: ${config.filename}`);

    // 使用<a>标签下载文件（网页环境标准方式）
    const link = document.createElement('a');
    link.href = objectUrl;
    link.download = config.filename;
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // 下载开始后立即释放Object URL以节省内存
    URL.revokeObjectURL(objectUrl);

    console.log(`✅ 文件保存成功: ${config.filename}`);

    return { success: true, filename: config.filename };

  } catch (error) {
    console.error('网页端下载失败:', error);
    throw error;
  }
}

// 实际的保存API调用（在网站端执行，避免扩展限制）
export function saveM3u8File(blob: Blob, filename: string): Promise<{ success: boolean; filename: string }> {
  return new Promise((resolve, reject) => {
    try {
      console.log(`开始保存M3U8文件: ${filename}, 大小: ${(blob.size / 1024 / 1024).toFixed(1)}MB`);

      // 使用简单的下载方式，确保文件名正确
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename; // 确保文件名正确设置
      a.style.display = 'none';

      document.body.appendChild(a);
      a.click(); // 触发浏览器下载
      document.body.removeChild(a);

      // 立即释放 Object URL 以节省内存
      URL.revokeObjectURL(url);

      console.log(`✅ M3U8文件保存成功: ${filename}`);
      resolve({ success: true, filename });
    } catch (error) {
      console.error(`❌ M3U8文件保存失败: ${(error as Error).message}`);
      reject(error);
    }
  });
}

// 格式化文件大小
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

// 格式化下载速度
export function formatSpeed(bytesPerSecond: number): string {
  return formatFileSize(bytesPerSecond) + '/s';
}

// 保存已下载的文件
export function saveDownloadedFile(blobUrl: string, filename: string): Promise<{ success: boolean; filename: string }> {
  return new Promise((resolve, reject) => {
    try {
      console.log(`开始保存文件: ${filename}`);

      // 创建下载链接
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = filename;
      link.style.display = 'none';

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      console.log(`✅ 文件保存成功: ${filename}`);
      resolve({ success: true, filename });
    } catch (error) {
      console.error(`❌ 文件保存失败: ${(error as Error).message}`);
      reject(error);
    }
  });
}

// 创建下载控制器
export function createDownloadController(config: DownloadConfig): DownloadController {
  return new DownloadController(config);
}
